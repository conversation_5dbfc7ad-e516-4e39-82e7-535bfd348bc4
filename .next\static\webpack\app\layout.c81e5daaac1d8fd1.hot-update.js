"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cf27d240411a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjZjI3ZDI0MDQxMWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _meshsdk_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_meshsdk_core__WEBPACK_IMPORTED_MODULE_2__]);\n_meshsdk_core__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\n_s(useWallet, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction WalletProvider(param) {\n    let { children } = param;\n    _s1();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getAvailableWallets = ()=>{\n        var _window_cardano, _window_cardano1, _window_cardano2, _window_cardano3;\n        if (false) {}\n        const wallets = [];\n        if ((_window_cardano = window.cardano) === null || _window_cardano === void 0 ? void 0 : _window_cardano.lace) wallets.push('lace');\n        if ((_window_cardano1 = window.cardano) === null || _window_cardano1 === void 0 ? void 0 : _window_cardano1.eternl) wallets.push('eternl');\n        if ((_window_cardano2 = window.cardano) === null || _window_cardano2 === void 0 ? void 0 : _window_cardano2.nami) wallets.push('nami');\n        if ((_window_cardano3 = window.cardano) === null || _window_cardano3 === void 0 ? void 0 : _window_cardano3.flint) wallets.push('flint');\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if (false) {}\n        setConnecting(true);\n        try {\n            const browserWallet = await _meshsdk_core__WEBPACK_IMPORTED_MODULE_2__.BrowserWallet.enable(walletName);\n            setWallet(browserWallet);\n            // Get wallet address\n            const addresses = await browserWallet.getUsedAddresses();\n            if (addresses.length > 0) {\n                setAddress(addresses[0]);\n            }\n            // Get wallet balance\n            try {\n                const balance = await browserWallet.getBalance();\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        localStorage.removeItem('vintrek_wallet');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], []);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s1(WalletProvider, \"dSBI9X6BQq4oMvol2/Gn4R+d8Wg=\");\n_c = WalletProvider;\nvar _c;\n$RefreshReg$(_c, \"WalletProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/WalletProvider.tsx\n"));

/***/ })

});