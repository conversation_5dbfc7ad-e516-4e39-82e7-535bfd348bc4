'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { BrowserWallet } from '@meshsdk/core'

interface WalletContextType {
  wallet: BrowserWallet | null
  connected: boolean
  connecting: boolean
  address: string | null
  balance: string | null
  authenticated: boolean
  connect: (walletName: string) => Promise<void>
  disconnect: () => void
  authenticate: () => Promise<boolean>
  signMessage: (message: string) => Promise<string | null>
  getAvailableWallets: () => string[]
}

const WalletContext = createContext<WalletContextType | undefined>(undefined)

export function useWallet() {
  const context = useContext(WalletContext)
  if (context === undefined) {
    throw new Error('useWallet must be used within a WalletProvider')
  }
  return context
}

interface WalletProviderProps {
  children: ReactNode
}

export function WalletProvider({ children }: WalletProviderProps) {
  const [wallet, setWallet] = useState<BrowserWallet | null>(null)
  const [connected, setConnected] = useState(false)
  const [connecting, setConnecting] = useState(false)
  const [address, setAddress] = useState<string | null>(null)
  const [balance, setBalance] = useState<string | null>(null)
  const [authenticated, setAuthenticated] = useState(false)

  const getAvailableWallets = () => {
    if (typeof window === 'undefined') return []
    
    const wallets = []
    if (window.cardano?.lace) wallets.push('lace')
    if (window.cardano?.eternl) wallets.push('eternl')
    if (window.cardano?.nami) wallets.push('nami')
    if (window.cardano?.flint) wallets.push('flint')
    
    return wallets
  }

  const connect = async (walletName: string) => {
    if (typeof window === 'undefined') return
    
    setConnecting(true)
    try {
      const browserWallet = await BrowserWallet.enable(walletName)
      setWallet(browserWallet)
      
      // Get wallet address
      const addresses = await browserWallet.getUsedAddresses()
      if (addresses.length > 0) {
        setAddress(addresses[0])
      }
      
      // Get wallet balance
      try {
        const balance = await browserWallet.getBalance()
        setBalance(balance)
      } catch (error) {
        console.warn('Could not fetch balance:', error)
        setBalance('0')
      }
      
      setConnected(true)
      
      // Store connection state
      localStorage.setItem('vintrek_wallet', walletName)
    } catch (error) {
      console.error('Failed to connect wallet:', error)
      throw error
    } finally {
      setConnecting(false)
    }
  }

  const signMessage = async (message: string): Promise<string | null> => {
    if (!wallet || !address) {
      throw new Error('Wallet not connected')
    }

    try {
      const signature = await wallet.signData(address, message)
      return signature.signature
    } catch (error) {
      console.error('Failed to sign message:', error)
      return null
    }
  }

  const authenticate = async (): Promise<boolean> => {
    if (!wallet || !address) {
      console.error('Wallet not connected')
      return false
    }

    try {
      const timestamp = Date.now()
      const message = `VinTrek Authentication\nAddress: ${address}\nTimestamp: ${timestamp}`

      const signature = await signMessage(message)
      if (signature) {
        setAuthenticated(true)
        localStorage.setItem('vintrek_auth', JSON.stringify({
          address,
          timestamp,
          signature
        }))
        return true
      }
      return false
    } catch (error) {
      console.error('Authentication failed:', error)
      return false
    }
  }

  const disconnect = () => {
    setWallet(null)
    setConnected(false)
    setAddress(null)
    setBalance(null)
    setAuthenticated(false)
    localStorage.removeItem('vintrek_wallet')
    localStorage.removeItem('vintrek_auth')
  }

  // Auto-reconnect on page load
  useEffect(() => {
    const savedWallet = localStorage.getItem('vintrek_wallet')
    const savedAuth = localStorage.getItem('vintrek_auth')

    if (savedWallet && getAvailableWallets().includes(savedWallet)) {
      connect(savedWallet).then(() => {
        // Check if we have valid authentication
        if (savedAuth) {
          try {
            const authData = JSON.parse(savedAuth)
            const now = Date.now()
            const authAge = now - authData.timestamp

            // Authentication is valid for 24 hours
            if (authAge < 24 * 60 * 60 * 1000 && authData.address === address) {
              setAuthenticated(true)
            }
          } catch (error) {
            console.error('Invalid auth data:', error)
            localStorage.removeItem('vintrek_auth')
          }
        }
      }).catch(console.error)
    }
  }, [address])

  const value: WalletContextType = {
    wallet,
    connected,
    connecting,
    address,
    balance,
    authenticated,
    connect,
    disconnect,
    authenticate,
    signMessage,
    getAvailableWallets,
  }

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  )
}
