"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _meshsdk_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_meshsdk_core__WEBPACK_IMPORTED_MODULE_2__]);\n_meshsdk_core__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\n_s(useWallet, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction WalletProvider(param) {\n    let { children } = param;\n    _s1();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [authenticated, setAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getAvailableWallets = ()=>{\n        var _window_cardano, _window_cardano1, _window_cardano2, _window_cardano3;\n        if (false) {}\n        const wallets = [];\n        if ((_window_cardano = window.cardano) === null || _window_cardano === void 0 ? void 0 : _window_cardano.lace) wallets.push('lace');\n        if ((_window_cardano1 = window.cardano) === null || _window_cardano1 === void 0 ? void 0 : _window_cardano1.eternl) wallets.push('eternl');\n        if ((_window_cardano2 = window.cardano) === null || _window_cardano2 === void 0 ? void 0 : _window_cardano2.nami) wallets.push('nami');\n        if ((_window_cardano3 = window.cardano) === null || _window_cardano3 === void 0 ? void 0 : _window_cardano3.flint) wallets.push('flint');\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if (false) {}\n        setConnecting(true);\n        try {\n            const browserWallet = await _meshsdk_core__WEBPACK_IMPORTED_MODULE_2__.BrowserWallet.enable(walletName);\n            setWallet(browserWallet);\n            // Get wallet address\n            const addresses = await browserWallet.getUsedAddresses();\n            if (addresses.length > 0) {\n                setAddress(addresses[0]);\n            }\n            // Get wallet balance\n            try {\n                const balance = await browserWallet.getBalance();\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const signMessage = async (message)=>{\n        if (!wallet || !address) {\n            throw new Error('Wallet not connected');\n        }\n        try {\n            const signature = await wallet.signData(address, message);\n            return signature.signature;\n        } catch (error) {\n            console.error('Failed to sign message:', error);\n            return null;\n        }\n    };\n    const authenticate = async ()=>{\n        if (!wallet || !address) {\n            console.error('Wallet not connected');\n            return false;\n        }\n        try {\n            const timestamp = Date.now();\n            const message = \"VinTrek Authentication\\nAddress: \".concat(address, \"\\nTimestamp: \").concat(timestamp);\n            const signature = await signMessage(message);\n            if (signature) {\n                setAuthenticated(true);\n                localStorage.setItem('vintrek_auth', JSON.stringify({\n                    address,\n                    timestamp,\n                    signature\n                }));\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error('Authentication failed:', error);\n            return false;\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        setAuthenticated(false);\n        localStorage.removeItem('vintrek_wallet');\n        localStorage.removeItem('vintrek_auth');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            const savedAuth = localStorage.getItem('vintrek_auth');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).then({\n                    \"WalletProvider.useEffect\": ()=>{\n                        // Check if we have valid authentication\n                        if (savedAuth) {\n                            try {\n                                const authData = JSON.parse(savedAuth);\n                                const now = Date.now();\n                                const authAge = now - authData.timestamp;\n                                // Authentication is valid for 24 hours\n                                if (authAge < 24 * 60 * 60 * 1000 && authData.address === address) {\n                                    setAuthenticated(true);\n                                }\n                            } catch (error) {\n                                console.error('Invalid auth data:', error);\n                                localStorage.removeItem('vintrek_auth');\n                            }\n                        }\n                    }\n                }[\"WalletProvider.useEffect\"]).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], [\n        address\n    ]);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_s1(WalletProvider, \"bTOXdwl0iQNN6Jr4CJk1Y8/6RdM=\");\n_c = WalletProvider;\nvar _c;\n$RefreshReg$(_c, \"WalletProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/WalletProvider.tsx\n"));

/***/ })

});